<?php
/*
Template Name: <PERSON><PERSON><PERSON> matural<PERSON>
*/

get_header();

?>

<section class="hero">
    <div class="dd-container" style="height: 100%;">
        <div class="hero__content">
            <div class="hero__left">
                <h1><?php _e('Ku<PERSON><PERSON> ', DD_TEXTDOMAIN); ?><span
                        class="color-secondary"><?php _e('maturalne', DD_TEXTDOMAIN); ?></span>
                </h1>
                <p><?php _e('Intensywne weekendowe kursy przygotowujące do Matury w zakresie podstawowym i rozszerzonym z języka angielskiego i polskiego.', DD_TEXTDOMAIN); ?>
                </p>
                <a href="#form" class="button"><?php _e('Zapisz się na egzamin', DD_TEXTDOMAIN); ?></a>
            </div>
            <div class="hero__right">
                <picture>
                    <source media="(max-width: 991px)"
                        srcset="<?= esc_url(get_template_directory_uri() . '/assets/img/graduation-exam-courses-mobile.webp'); ?>">
                    <img src="<?= esc_url(get_template_directory_uri() . '/assets/img/graduation-exam-courses.webp'); ?>"
                        alt="<?= esc_attr__('Obrazek w sekcji Hero', DD_TEXTDOMAIN); ?>">
                </picture>
            </div>
        </div>
    </div>
</section>

<main id="main-content" class="template dd-container">
    <section class="section_without_image">
        <h2><?php the_field('wstep_naglowek'); ?></h2>
        <div class="section_without_image__content">
            <div class="section_without_image__left"><?php the_field('wstep__tresc_lewa_kolumna'); ?></div>
            <div class="section_without_image__right"><?php the_field('wstep__tresc_prawa_kolumna'); ?></div>
    </section>
    <section class="courses-for-you">
        <h2 class="section-title">
            <span class="color-secondary"><?php _e('Oferujemy kursy z ', DD_TEXTDOMAIN); ?></span>
            <?php _e('języka angielskiego i polskiego', DD_TEXTDOMAIN); ?>
        </h2>
        <div class="courses-for-you__content">
            <div class="courses-for-you__column">
                <img src="<?php echo get_template_directory_uri() . '/assets/img/courses-for-you-english.webp' ?>"
                    alt="">
            </div>
            <div class="courses-for-you__column">
                <img src="<?php echo get_template_directory_uri() . '/assets/img/courses-for-you-polish.webp' ?>"
                    alt="">
            </div>
        </div>
        <?php $steps = [
            [
                'title' => __('<strong>PODSTAWA:</strong> 2600 zł za 90 h.', DD_TEXTDOMAIN),
                'icons' => [
                    [
                        'icon' => get_template_directory_uri() . '/assets/img/icon-single-file.svg',
                        'item' => __('<strong>Podręczniki i materiały do pracy,</strong>', DD_TEXTDOMAIN),
                    ],
                    [
                        'icon' => get_template_directory_uri() . '/assets/img/icon-tasks-to-do.svg',
                        'item' => __('<strong>Przybory do pisania oraz zeszyt,</strong>', DD_TEXTDOMAIN),
                    ],
                    [
                        'icon' => get_template_directory_uri() . '/assets/img/icon-list-file.svg',
                        'item' => __('<strong>Arkusze maturalne oraz karty pracy,</strong>', DD_TEXTDOMAIN),
                    ],
                    [
                        'icon' => get_template_directory_uri() . '/assets/img/icon-bottle.svg',
                        'item' => __('<strong>Picie na każdych zajęciach,</strong>', DD_TEXTDOMAIN),
                    ],
                    [
                        'icon' => get_template_directory_uri() . '/assets/img/list-icon.svg',
                        'item' => __('<strong>Zadania domowe,</strong>', DD_TEXTDOMAIN),
                    ],
                    [
                        'icon' => get_template_directory_uri() . '/assets/img/icon-education-materials.svg',
                        'item' => __('<strong>Testy i kartkówki sprawdzające wiedzę.</strong>', DD_TEXTDOMAIN),
                    ],
                ],
            ],
            [
                'title' => __('<strong>ROZSZERZENIE:</strong> 2600 zł za 90 h.', DD_TEXTDOMAIN),
                'icons' => [
                    [
                        'icon' => get_template_directory_uri() . '/assets/img/icon-single-file.svg',
                        'item' => __('<strong>Podręczniki i materiały do pracy,</strong>', DD_TEXTDOMAIN),
                    ],
                    [
                        'icon' => get_template_directory_uri() . '/assets/img/icon-tasks-to-do.svg',
                        'item' => __('<strong>Przybory do pisania oraz zeszyt,</strong>', DD_TEXTDOMAIN),
                    ],
                    [
                        'icon' => get_template_directory_uri() . '/assets/img/icon-list-file.svg',
                        'item' => __('<strong>Arkusze maturalne oraz karty pracy,</strong>', DD_TEXTDOMAIN),
                    ],
                    [
                        'icon' => get_template_directory_uri() . '/assets/img/icon-bottle.svg',
                        'item' => __('<strong>Picie na każdych zajęciach,</strong>', DD_TEXTDOMAIN),
                    ],
                    [
                        'icon' => get_template_directory_uri() . '/assets/img/list-icon.svg',
                        'item' => __('<strong>Zadania domowe,</strong>', DD_TEXTDOMAIN),
                    ],
                    [
                        'icon' => get_template_directory_uri() . '/assets/img/icon-education-materials.svg',
                        'item' => __('<strong>Testy i kartkówki sprawdzające wiedzę.</strong>', DD_TEXTDOMAIN),
                    ],
                ],
            ],
        ]; ?>

        <div class="dd-steps-section boxes">
            <div class="wrapper">
                <h2 class="section-title"><?php the_field('poziom_egzaminu__naglowek'); ?></h2>
            </div>
            <div class="dd-steps-wrapper">
                <div class="dd-step">
                    <div class="dd-step-title">
                        <h2><?php the_field('poziom_egzaminu__podstawa_naglowek'); ?></h2>
                    </div>

                    <div class="dd-step-items">
                        <span><?php the_field('poziom_egzaminu__podstawa_opis'); ?></span>
                        <?php if( have_rows('poziom_egzaminu__podstawa_lista') ): ?>
                            <?php while( have_rows('poziom_egzaminu__podstawa_lista') ) : the_row(); ?>
                            <div class="dd-step-item">
                                <img src="<?php the_sub_field('ikona'); ?>" alt="">
                                <span><?php the_sub_field('tekst'); ?></span>
                            </div>
                            <?php endwhile; ?>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="dd-step">
                    <div class="dd-step-title">
                        <h2><?php the_field('poziom_egzaminu__rozszerzenie_naglowek'); ?></h2>
                    </div>

                    <div class="dd-step-items">
                        <span><?php the_field('poziom_egzaminu__rozszerzenie_opis'); ?></span>
                        <?php if( have_rows('poziom_egzaminu__rozszerzenie_lista') ): ?>
                            <?php while( have_rows('poziom_egzaminu__rozszerzenie_lista') ) : the_row(); ?>
                                <div class="dd-step-item">
                                    <img src="<?php the_sub_field('ikona'); ?>" alt="">
                                    <span><?php the_sub_field('tekst'); ?></span>
                                </div>
                            <?php endwhile; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

    </section>
    <section class="dd-exam-info-section">
        <h2><?php the_field('o_kursie__naglowek'); ?></h2>
        <div class="grid two-columns">
            <div class="left-side">
                <?php if( have_rows('o_kursie__lista_lewa') ): ?>
                <div class="item">
                    <div class="insider">
                        <span class="number">1.</span>
                        <strong><?php _e('INFORMACJE OGÓLNE', DD_TEXTDOMAIN); ?></strong>
                    </div>
                    <div class="content">
                        <p><?php _e('Nasi Trenerzy Językowi to starannie wyselekcjonowane grono, które współpracuje z Lingua Nova od wielu lat. Na przestrzeni tej współpracy stworzyliśmy dla każdego z naszych Trenerów odpowiednie portfolio, dzięki któremu wiemy, która tematyka jest specjalnością danego Nauczyciela. Ta wnikliwa i długofalowa obserwacja sprawia, że nasza Kadra spełnia wszystkie oczekiwania i uczy języka branżowego w określonych zakresach nie tylko profesjonalnie, ale również z wielką pasją.', DD_TEXTDOMAIN); ?>
                        </p>
                    </div>
                </div>
                <?php endif; ?>

            </div>
            <div class="right-side">
                <div class="item">
                    <div class="insider">
                        <span class="number">3.</span>
                        <strong><?php _e('CELE I ZAŁOŻENIA', DD_TEXTDOMAIN); ?></strong>
                    </div>
                    <div class="content">
                        <p><?php _e('Każdy z siedmiu kursów zawiera 45 lekcji po 45minut. Aby zoptymalizować Twój czas, przygotowaliśmy harmonogram 22 spotkań po 90 min. (dwie lekcje 45-minutowe na spotkanie) w czasie których przerabiany jest materiał branżowy, oraz jedno spotkanie 45-minutowe, po którym może się odbyć egzamin branżowy, trwający również 45 minut.', DD_TEXTDOMAIN); ?>
                        </p>
                    </div>
                </div>
                <div class="item">
                    <div class="insider">
                        <span class="number">4.</span>
                        <strong><?php _e('OCENA UCZNIA I KONTROLA POSTĘPÓW', DD_TEXTDOMAIN); ?></strong>
                    </div>
                    <div class="content">
                        <p><?php _e('Każdy nasz kurs rozpoczyna się od bezpłatnego kompleksowego audytu językowego, który sprawdza również kompetencję rozumienia ze słuchu oraz umiejętność formułowania wypowiedzi werbalnej. Kursy branżowe przygotowane są na poziomy językowe od A2 do B2. Jeżeli Twój poziom językowy nie pozwoli na natychmiastowe podjecie kursu, powiadomimy Cię o tym i podpowiemy najlepsze rozwiązanie tej sytuacji.', DD_TEXTDOMAIN); ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="section-faq">
        <?php $faqItems = [
            [
                'question' => __('CO ZYSKUJECIE DECYDUJĄC SIĘ NA INTENSYWNY KURS Z LINGUA NOVA?', DD_TEXTDOMAIN),
                'icon' => get_template_directory_uri() . '/assets/img/icon-gift.svg',
                'answers' => [
                    __('zajęcia prowadzone przez doświadczonego nauczyciela', DD_TEXTDOMAIN),
                    __('dostęp do materiałów maturalnych', DD_TEXTDOMAIN),
                    __('sprawdzenie jakie są Wasze mocne strony i jakie umiejętności wymagają usprawnienia', DD_TEXTDOMAIN),
                    __('nadrobienie zaległości', DD_TEXTDOMAIN),
                    __('nabranie biegłości w wykonywaniu zadań maturalnych', DD_TEXTDOMAIN),
                    __('nowoczesne i przyjemne dla ucznia podejście do nauki', DD_TEXTDOMAIN),
                ],
            ],
            [
                'question' => __('ILE TO KOSZTUJE', DD_TEXTDOMAIN),
                'icon' => get_template_directory_uri() . '/assets/img/icon-wallet.svg',
                'answers' => [
                    __('wpłata jednorazowa: 1499 zł (miejsce w grupie potwierdzone po otrzymaniu wpłaty)<br>płacąc jednorazowo oszczędzasz na co najmniej 9 biletów do kina lub 4 wypasione pizze', DD_TEXTDOMAIN),
                    __('opłata w 2 ratach: 832 zł (miejsce w grupie potwierdzone po otrzymaniu wpłaty pierwszej raty; druga rata powinna być uiszczona do 30. godziny kursu)', DD_TEXTDOMAIN),
                    __('co zawiera cena:', DD_TEXTDOMAIN),
                    __('67 godzin lekcyjnych kursu', DD_TEXTDOMAIN),
                    __('roczny dostęp do platformy e-learningowej', DD_TEXTDOMAIN),
                ],
            ],
            [
                'question' => __('ORGANIZACJA KURSU', DD_TEXTDOMAIN),
                'icon' => get_template_directory_uri() . '/assets/img/icon-faq-calendar.svg',
                'answers' => [
                    __('zajęcia odbywają się stacjonarnie', DD_TEXTDOMAIN),
                    __('do wyboru są dwa kursy: (1) przygotowujący do egzaminu na poziomie podstawowym i (2) przygotowujący do egzaminu na poziomie rozszerzonym', DD_TEXTDOMAIN),
                    __('każdy uczestnik kursu otrzymuje swój egzemplarz repertorium w cenie kursu', DD_TEXTDOMAIN),
                    __('grupy 18 - 22 osób', DD_TEXTDOMAIN),
                    __('zajęcia 3 godzinne z 15 minutową przerwą w trakcie', DD_TEXTDOMAIN),
                    __('zajęcia raz w tygodniu w sumie 30 jednostek lekcyjnych', DD_TEXTDOMAIN),
                    __('dostęp do platformy edukacyjnej: dodatkowe ćwiczenia, testy i materiały', DD_TEXTDOMAIN),
                ],
            ],
        ]; ?>

        <div class="wrapper">
            <h2 class="section-title">
                <?php _e('Dlaczego warto ', DD_TEXTDOMAIN); ?>
                <span
                    class="color-secondary"><?php _e('wybrać kursy maturalne z Lingua Nova?', DD_TEXTDOMAIN); ?></span>
            </h2>
        </div>
        <div class="faq-container">
            <?php $lastIndex = count($faqItems) - 1;
            foreach ($faqItems as $index => $item):
            $isLast = ($index === $lastIndex); ?>

            <div class="faq-item<?php echo $isLast ? ' open' : ''; ?>">
                <div class="faq-question">
                    <img class="faq-icon" src="<?php echo esc_url($item['icon']); ?>"
                        alt="<?php echo esc_attr($item['question']); ?>">
                    <h3 class="faq-text"><?php echo esc_html($item['question']); ?></h3>
                </div>
                <div class="faq-answer" style="<?php echo $isLast ? 'display: block;' : ''; ?>">
                    <ul>
                        <?php foreach ($item['answers'] as $answer): ?>
                        <li><?php echo esc_html($answer); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </section>
    <section class="section-newsletter">
        <div class="left-right">
            <div class="left">
                <div class="newsletter full-height">
                    <div class="left-side">
                        <h2><?php _e('Zacznijmy pracować <span class="color-secondary">nad Twoimi rezultatami!</span>', DD_TEXTDOMAIN) ?>
                        </h2>
                        <p><?php _e('<strong>Skontaktuj się z nami i wypełnij formularz kontaktowy</strong> – dobierzemy odpowiedni program i zajmiemy się resztą.', DD_TEXTDOMAIN); ?>
                        </p>
                    </div>
                    <div class="right-side">
                        <img src="<?php echo get_template_directory_uri() . '/assets/img/newsletter-big.webp' ?>"
                            alt="<?php _e('Obrazek newslettera', DD_TEXTDOMAIN) ?>">
                    </div>
                </div>
            </div>
            <div class="right" id="form">
                <?php echo do_shortcode('[contact-form-7 id="1d42717" title="Kurs maturalny"]'); ?>
            </div>
        </div>
    </section>
</main>

<script>
document.addEventListener('dd:tabActivated', function(event) {
    const tabId = event.detail.tabId;
    const contents = document.querySelectorAll('.exam-tab-content .tab');

    contents.forEach(c => c.classList.remove('active'));

    const contentToActivate = document.getElementById(tabId);
    if (contentToActivate) contentToActivate.classList.add('active');
});

jQuery(document).ready(function($) {
    $(".faq-item").on("click", function() {
        const $clicked = $(this);

        if ($clicked.hasClass("open")) {
            $clicked.removeClass("open").find(".faq-answer").slideUp(300, "swing");
        } else {
            $(".faq-item").removeClass("open").find(".faq-answer").slideUp(300, "swing");
            $clicked.addClass("open").find(".faq-answer").slideDown(300, "swing");
        }
    });
});

jQuery(document).ready(function($) {
    $(".dd-exam-info-section .item").on("click", function() {
        if ($(window).width() >= 991) {
            return;
        }

        const $item = $(this);
        const $content = $item.find(".content");

        $item.toggleClass("open");

        if ($content.is(":visible")) {
            $content.slideUp(300, "swing");
        } else {
            $content
                .css({
                    display: "block"
                })
                .hide()
                .slideDown(300, "swing");
        }
    });
});
</script>

<?php get_footer(); ?>